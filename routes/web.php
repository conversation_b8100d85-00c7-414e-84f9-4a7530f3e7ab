<?php

use Illuminate\Http\Request;
use Sina\Shuttle\Facades\Shuttle;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\CartController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\AwwardsController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RegisterController;
use App\Http\Controllers\ConferenceController;
use App\Http\Controllers\FestivalController;
use App\Http\Controllers\SocialAuthController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\Shuttle\ExportController as ShuttleExportController;
use App\Http\Controllers\Shuttle\AwwardsController as ShuttleAwwardsController;
use App\Http\Controllers\Shuttle\ConferenceController as ShuttleConferenceController;
use App\Http\Controllers\Shuttle\FestivalController as ShuttleFestivalController;
use App\Http\Controllers\Shuttle\TransactionController as ShuttleTransactionController;
use App\Http\Controllers\SearchController;
use App\Models\Review;
use Carbon\Carbon;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('artisan', function () {
//    Artisan::call('storage:link');
//    $outputt = Artisan::output();
//    print $outputt;
    \Sina\Shuttle\Models\Admin::updateOrCreate([
        'email' => '<EMAIL>',],[
        'password' => bcrypt('Maka123')
    ]);
});

// Review show route for modal
Route::get('mypanel/scaffold-interface/{scaffoldInterface}/show/{id}', function ($scaffoldInterface, $id) {
    $review = Review::findOrFail($id);
    return response()->json($review);
});

Route::get('check-page-components/{pageId?}', function ($pageId = 1) {

    $page = \Sina\Shuttle\Models\Page::with(['components' => function($query) {
        $query->orderBy('position');
    }])->find($pageId);

    if (!$page) {
        return "Page with ID $pageId not found";
    }

    $html = "<h2>Page: {$page->title}</h2>";
    $html .= "<p>URL: {$page->url}</p>";
    $html .= "<p>Total Components: " . $page->components->count() . "</p>";

    // Check template file
    $templateFile = resource_path("views/sections/en/{$pageId}.blade.php");
    $html .= "<p>Template File: " . $templateFile . "</p>";
    $html .= "<p>Template Exists: " . (file_exists($templateFile) ? 'YES' : 'NO') . "</p>";
    if (file_exists($templateFile)) {
        $html .= "<p>Template Content:</p>";
        $html .= "<pre>" . htmlspecialchars(file_get_contents($templateFile)) . "</pre>";
    }

    if ($page->components->count() > 0) {
        $html .= "<h3>Components:</h3>";
        $html .= "<ul>";
        foreach ($page->components as $index => $component) {
            $html .= "<li>";
            $html .= "[$index] Component: <strong>{$component->name}</strong><br>";
            $html .= "Locale: {$component->pivot->locale}<br>";
            $html .= "Position: {$component->pivot->position}<br>";
            $html .= "Settings: " . json_encode($component->pivot->setting) . "<br>";
            $html .= "</li><br>";
        }
        $html .= "</ul>";

        // Test what should be in template
        $html .= "<h3>What Template Should Contain:</h3>";
        $html .= "<pre>";
        $html .= htmlspecialchars('@foreach($page->components as $component)') . "\n";
        $html .= htmlspecialchars('<x-shuttle-dynamic-component :name="$component->name" :c="$component" :data="$component->pivot->setting"></x-shuttle-dynamic-component>') . "\n";
        $html .= htmlspecialchars('@endforeach');
        $html .= "</pre>";

    } else {
        $html .= "<p>No components found for this page.</p>";
    }

    // Add manual template creation button
    $html .= "<hr><h3>Manual Actions:</h3>";
    $html .= "<a href='/fix-template/{$pageId}' style='background: #007cba; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Fix Template File</a>";

    return $html;
});

Route::get('fix-template/{pageId}', function ($pageId) {
    try {
        $page = \Sina\Shuttle\Models\Page::with(['components'])->find($pageId);

        if (!$page) {
            return "Page with ID $pageId not found";
        }

        $sourceFile = resource_path("views/sections/en/");

        if(!is_dir($sourceFile)){
            mkdir($sourceFile, 0755, true);
        }

        // Generate universal template that works with any number of components
        $contents = '@foreach($page->components as $component)' . "\n";
        $contents .= '<x-shuttle-dynamic-component :name="$component->name" :c="$component" :data="$component->pivot->setting"></x-shuttle-dynamic-component>' . "\n";
        $contents .= '@endforeach';

        file_put_contents($sourceFile . $pageId . ".blade.php", $contents);

        return "Template file created successfully for page {$pageId}! <br><br><a href='/check-page-components/{$pageId}'>Check Again</a> | <a href='/{$page->url}'>View Page</a>";

    } catch (Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

Route::get('debug-components', function () {
    $components = \Sina\Shuttle\Models\Component::all();

    $html = "<h2>All Components in Database:</h2>";
    $html .= "<ul>";
    foreach ($components as $component) {
        $html .= "<li>ID: {$component->id} - Name: {$component->name} - Display: {$component->display_name}</li>";
    }
    $html .= "</ul>";

    return $html;
});
// Test route შექმენით
Route::get('/test-mail', function() {
    try {
        Mail::raw('Test email', function($message) {
            $message->to('<EMAIL>')->subject('Test');
        });
        return 'Email sent successfully';
    } catch (\Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
});
Route::get('/test-awward-transaction/{packageId}', function ($packageId) {
    // You might need to authenticate a user for this to work correctly
    // For a quick test, you can manually log in a user or create a temporary one
    $user = \App\Models\User::first(); // Or any existing user
    if (!$user) {
        return "Please create a user in your database to test this route.";
    }
    auth()->login($user);

    $package = \App\Models\AwwardPackage::find($packageId);

    if (!$package) {
        return "AwwardPackage with ID {$packageId} not found.";
    }

    $request = new Illuminate\Http\Request([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'mobile' => '1234567890',
        'company' => 'Test Company',
        'job' => 'Test Job',
        'address1' => '123 Test St',
        'address2' => '',
        'city' => 'Test City',
        'zip_code' => '12345',
        'country' => 'Test Country',
        'payment_method' => 'test', // A dummy payment method
    ]);

    $controller = new App\Http\Controllers\TransactionController(new App\Service\TransactionService()); // Instantiate with a dummy service
    return $controller->awward($request, $package);

})->name('test.awward.transaction');
Shuttle::routes();

// Add map fields to new_contact component
Route::get('/add-map-fields', function () {
    // Get current settings
    $currentSettings = \DB::select("SELECT settings FROM shuttle_components WHERE id = 29");

    if (empty($currentSettings)) {
        return 'Component not found';
    }

    $settings = json_decode($currentSettings[0]->settings, true);

    // Add map latitude field
    $mapLatField = [
        'id' => 500, // Use high ID to avoid conflicts
        'add' => 0,
        'ord' => 0,
        'edit' => 0,
        'read' => 0,
        'type' => 'text',
        'field' => 'map-lat',
        'browse' => 0,
        'delete' => 0,
        'details' => [],
        'children' => [],
        'last_upd' => time(),
        'required' => 0,
        'parent_id' => 0,
        'rowable_id' => 29,
        'display_name' => 'Map Latitude',
        'rowable_type' => 'Sina\\Shuttle\\Models\\Component'
    ];

    // Add map longitude field
    $mapLngField = [
        'id' => 501, // Use high ID to avoid conflicts
        'add' => 0,
        'ord' => 0,
        'edit' => 0,
        'read' => 0,
        'type' => 'text',
        'field' => 'map-lng',
        'browse' => 0,
        'delete' => 0,
        'details' => [],
        'children' => [],
        'last_upd' => time(),
        'required' => 0,
        'parent_id' => 0,
        'rowable_id' => 29,
        'display_name' => 'Map Longitude',
        'rowable_type' => 'Sina\\Shuttle\\Models\\Component'
    ];

    // Add new fields to settings
    $settings[] = $mapLatField;
    $settings[] = $mapLngField;

    // Update component settings
    \DB::update("UPDATE shuttle_components SET settings = ? WHERE id = 29", [json_encode($settings)]);

    return 'Map fields added successfully!<br><br>' .
           '<a href="https://business-eagles.com/mypanel/page/component/29">Edit new_contact Component</a><br><br>' .
           '<a href="https://business-eagles.com/check-components-table">Check Updated Component</a>';
})->name('add-map-fields');

// Check pivot table structure only
Route::get('/check-contact-data', function () {
    $html = '<h1>Database Tables Check:</h1><br>';

    // Check shuttle_page_component table structure
    try {
        $columns = \DB::select("SHOW COLUMNS FROM shuttle_page_component");
        $html .= '<h2>shuttle_page_component Table Structure:</h2>';
        foreach ($columns as $column) {
            $html .= '<p><strong>' . $column->Field . '</strong> - ' . $column->Type . '</p>';
        }
    } catch (\Exception $e) {
        $html .= '<p>Error checking shuttle_page_component: ' . $e->getMessage() . '</p>';
    }

    // Check all tables with 'shuttle' in name
    $html .= '<br><h2>All Shuttle Tables:</h2>';
    $tables = \DB::select("SHOW TABLES LIKE 'shuttle%'");
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        $html .= '<p><strong>' . $tableName . '</strong></p>';
    }

    // Simple approach - let's check what data is available in the template
    $html .= '<br><h2>Template Data Check:</h2>';
    $html .= '<p>The easiest way is to check what $data contains in the new_contact.blade.php template.</p>';
    $html .= '<p>Let\'s add debug output to see what data is available.</p>';

    $html .= '<br><p><strong>Edit Contact Page:</strong> <a href="https://business-eagles.com/mypanel/page/7/edit">Edit Contact Page</a></p>';
    $html .= '<p><strong>Edit Component:</strong> <a href="https://business-eagles.com/mypanel/page/component/29">Edit new_contact Component</a></p>';

    return $html;
})->name('check-contact-data');

// Debug route for conference form3 modal
Route::post('form/{form}/submit', function(\App\Models\Form $form, \Illuminate\Http\Request $request) {
    \Log::info('=== FORM SUBMISSION DEBUG ===');
    \Log::info('Form ID: ' . $form->id);
    \Log::info('Form Name: ' . ($form->name ?? 'NULL'));
    \Log::info('Form PDF: ' . ($form->pdf ?? 'NULL'));
    \Log::info('PDF exists in storage: ' . ($form->pdf ? (\Storage::exists($form->pdf) ? 'YES' : 'NO') : 'N/A'));
    \Log::info('Request method: ' . $request->method());
    \Log::info('Request is AJAX: ' . ($request->ajax() ? 'YES' : 'NO'));
    \Log::info('Request data: ', $request->all());
    \Log::info('Model type: ' . ($request->model_type ?? 'NULL'));
    \Log::info('Model ID: ' . ($request->model_id ?? 'NULL'));

    // Check if this is conference form3 modal
    if ($request->model_type === 'conference') {
        \Log::info('=== CONFERENCE FORM3 MODAL DETECTED ===');
        $conference = \App\Models\Conference::find($request->model_id);
        if ($conference) {
            \Log::info('Conference title: ' . $conference->title);
            \Log::info('Conference form ID: ' . ($conference->form_id ?? 'NULL'));
        }
    }

    // If AJAX request, return JSON debug info
    if ($request->ajax()) {
        return response()->json([
            'debug' => true,
            'form_id' => $form->id,
            'form_pdf' => $form->pdf ?? 'NULL',
            'pdf_exists' => $form->pdf ? \Storage::exists($form->pdf) : false,
            'model_type' => $request->model_type,
            'model_id' => $request->model_id,
            'is_conference_form3' => $request->model_type === 'conference',
            'storage_path' => $form->pdf ? storage_path('app/'.$form->pdf) : null,
            'file_exists_on_disk' => $form->pdf ? file_exists(storage_path('app/'.$form->pdf)) : false,
            'message' => 'Debug info - form submission intercepted'
        ]);
    }

    // For non-AJAX, call original controller
    return app(\App\Http\Controllers\ContactController::class)->form($request, $form);
})->name('form.store');

// Debug route to test conference form3 modal
Route::get('/debug/conference-form3/{conference}', function(\App\Models\Conference $conference) {
    $debug = [
        'conference_id' => $conference->id,
        'conference_title' => $conference->title,
        'has_form' => $conference->form ? true : false,
        'form_id' => $conference->form_id ?? 'NULL',
    ];

    if ($conference->form) {
        $form = $conference->form;
        $debug['form_name'] = $form->name ?? 'NULL';
        $debug['form_pdf'] = $form->pdf ?? 'NULL';
        $debug['pdf_exists_in_storage'] = $form->pdf ? \Storage::exists($form->pdf) : false;
        $debug['pdf_storage_path'] = $form->pdf ? storage_path('app/'.$form->pdf) : null;
        $debug['pdf_file_exists_on_disk'] = $form->pdf ? file_exists(storage_path('app/'.$form->pdf)) : false;
        $debug['pdf_public_url'] = $form->pdf ? \Storage::url($form->pdf) : null;

        if ($form->pdf && \Storage::exists($form->pdf)) {
            $debug['pdf_size_bytes'] = \Storage::size($form->pdf);
            $debug['pdf_mime_type'] = \Storage::mimeType($form->pdf);
        }
    }

    return response()->json($debug, 200, [], JSON_PRETTY_PRINT);
})->name('debug.conference.form3');

// DEBUG: Simple form check
Route::get('/debug-form/{id}', function($id) {
    $form = \App\Models\Form::find($id);
    if (!$form) return response()->json(['error' => 'Form not found'], 404);

    $result = [
        'form_id' => $form->id,
        'form_name' => $form->name ?? 'No name',
        'form_pdf' => $form->pdf ?? 'No PDF field',
        'pdf_exists' => $form->pdf ? \Storage::exists($form->pdf) : false,
        'pdf_url' => $form->pdf ? \Storage::url($form->pdf) : null,
        'pdf_path' => $form->pdf ? storage_path('app/'.$form->pdf) : null,
        'file_exists' => $form->pdf ? file_exists(storage_path('app/'.$form->pdf)) : false,
    ];

    return response()->json($result);
});

Route::get('/debug-download/{id}', function($id) {
    $form = \App\Models\Form::find($id);
    if (!$form || !$form->pdf) return response('No PDF', 404);
    if (!\Storage::exists($form->pdf)) return response('PDF not found in storage', 404);
    return \Storage::download($form->pdf, 'test.pdf');
});
Route::post('contact/enquiry', [ContactController::class, 'enquiry'])->name('contact.enquiry');
Route::post('/contact', [ContactController::class, 'send'])->name('contact.send');

// Promo Code API routes
Route::post('api/promo-code/validate', function(\Illuminate\Http\Request $request) {
    try {
        \Log::info('Promo code validation request', $request->all());

        $request->validate([
            'code' => 'required|string',
            'amount' => 'required|numeric|min:0'
        ]);

        $promoCode = \App\Models\PromoCode::where('code', strtoupper($request->code))->first();

        if (!$promoCode) {
            return response()->json([
                'valid' => false,
                'message' => 'Promo code not found.'
            ], 404);
        }

        // Simple validation without calling model methods
        $isActive = $promoCode->is_active;
        $currentTime = \Carbon\Carbon::now('Asia/Tbilisi');
        $expiresAt = \Carbon\Carbon::parse($promoCode->expires_at, 'Asia/Tbilisi');
        $isNotExpired = $expiresAt > $currentTime;
        $hasUsageLeft = $promoCode->max_usage === null || $promoCode->usage_count < $promoCode->max_usage;

        \Log::info('Promo code validation details', [
            'code' => $promoCode->code,
            'expires_at' => $expiresAt->toDateTimeString(),
            'current_time' => $currentTime->toDateTimeString(),
            'is_active' => $isActive,
        ]);



// DEBUG: Inspect Form PDF status and try a direct download
Route::get('/debug/form/{form}', function(\Illuminate\Http\Request $request, \App\Models\Form $form) {
    $result = [
        'form_id' => $form->id,
        'form_pdf' => $form->pdf,
        'storage_exists' => $form->pdf ? \Storage::exists($form->pdf) : false,
        'storage_path' => $form->pdf ? storage_path('app/'.ltrim($form->pdf, '/')) : null,
        'public_url' => $form->pdf ? \Storage::url($form->pdf) : null,
        'readable' => $form->pdf ? is_readable(storage_path('app/'.ltrim($form->pdf, '/'))) : false,
        'filesize_bytes' => null,
        'storage_mime' => null,
        'file_mime' => null,
    ];

    $abs = $result['storage_path'];
    if ($abs && file_exists($abs)) {
        $result['filesize_bytes'] = @filesize($abs);
        try { $result['file_mime'] = @\Illuminate\Support\Facades\File::mimeType($abs); } catch (\Throwable $e) { $result['file_mime'] = 'mime-detect-error: '.$e->getMessage(); }
    }
    try { if ($form->pdf) { $result['storage_mime'] = @\Storage::mimeType($form->pdf); } } catch (\Throwable $e) { $result['storage_mime'] = 'storage-mime-error: '.$e->getMessage(); }

    return response()->json($result);
})->name('debug.form');

Route::get('/debug/form/{form}/download', function(\App\Models\Form $form) {
    if ($form->pdf && \Storage::exists($form->pdf)) {
        return \Storage::download($form->pdf, 'debug-form.pdf');
    }
    return response('PDF not found for form ID '.$form->id, 404);
})->name('debug.form.download');


        if (!$isActive || !$isNotExpired || !$hasUsageLeft) {
            $message = 'Promo code is ';
            if (!$isActive) {
                $message .= 'inactive.';
            } elseif (!$isNotExpired) {
                $message .= 'expired.';
            } else {
                $message .= 'no longer available.';
            }

            return response()->json([
                'valid' => false,
                'message' => $message
            ], 400);
        }

        $discountAmount = ($request->amount * $promoCode->discount_percentage) / 100;
        $finalAmount = $request->amount - $discountAmount;

        \Log::info('Promo code calculation', [
            'original_amount' => $request->amount,
            'discount_percentage' => $promoCode->discount_percentage,
            'discount_amount' => $discountAmount,
            'final_amount' => $finalAmount
        ]);

        return response()->json([
            'valid' => true,
            'message' => "Promo code applied! {$promoCode->discount_percentage}% discount.",
            'discount_percentage' => $promoCode->discount_percentage,
            'discount_amount' => number_format($discountAmount, 2),
            'original_amount' => number_format($request->amount, 2),
            'final_amount' => number_format($finalAmount, 2),
            'code' => $promoCode->code
        ]);

    } catch (\Exception $e) {
        \Log::error('Promo code validation error', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);

        return response()->json([
            'valid' => false,
            'message' => 'Error validating promo code: ' . $e->getMessage()
        ], 500);
    }
})->name('promo-code.validate');

Route::post('api/promo-code/apply', [App\Http\Controllers\PromoCodeController::class, 'apply'])->name('promo-code.apply');
Route::post('transaction', [TransactionController::class, 'store'])->name('transaction.store');
Route::post('transaction/{package}/conference', [TransactionController::class, 'conference'])->name('transaction.conference');
Route::post('transaction/{package}/awward', [TransactionController::class, 'awward'])->name('transaction.awward');
Route::post('transaction/{package}/festival', [TransactionController::class, 'festival'])->name('transaction.festival');
Route::post('cart/{courseLocation}', [CartController::class, 'store'])->name('cart.store');
Route::put('cart/{cart}', [CartController::class, 'update'])->name('cart.update');
Route::delete('cart/{cart}', [CartController::class, 'destroy'])->name('cart.destroy');
Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle'])->name('google.redirect');
Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.callback');

// JavaScript-based Google Sign-In (Plesk-friendly)
Route::post('/auth/google/token', [SocialAuthController::class, 'handleGoogleToken'])->name('google.token');

// Test POST route
Route::post('/test-post', function() {
    return response()->json(['message' => 'POST route works!', 'data' => request()->all()]);
});

// Simple Google token handler (without controller)
Route::post('/auth/google/simple', function() {
    try {
        $token = request()->input('token');

        if (!$token) {
            return response()->json(['error' => 'Token is required'], 400);
        }

        // Verify the token with Google using HTTP request
        $response = file_get_contents("https://oauth2.googleapis.com/tokeninfo?id_token=" . $token);
        $payload = json_decode($response, true);

        if (!$payload || isset($payload['error'])) {
            return response()->json(['error' => 'Invalid token'], 400);
        }

        // Verify the audience (client_id)
        if ($payload['aud'] !== env('GOOGLE_CLIENT_ID')) {
            return response()->json(['error' => 'Invalid client'], 400);
        }

        // Extract user information
        $googleId = $payload['sub'];
        $email = $payload['email'];
        $firstName = $payload['given_name'] ?? '';
        $lastName = $payload['family_name'] ?? '';
        $avatar = $payload['picture'] ?? '';

        // Check if user already exists
        $user = \App\Models\User::where('email', $email)->first();

        if (!$user) {
            // Create new user
            $user = \App\Models\User::create([
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => $email,
                'email_verified_at' => now(),
                'image' => $avatar,
                'password' => bcrypt(\Illuminate\Support\Str::random(16)),
                'google_id' => $googleId,
            ]);
        }

        \Illuminate\Support\Facades\Auth::login($user);

        return response()->json([
            'success' => true,
            'redirect' => '/profile',
            'user' => [
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json(['error' => 'Authentication failed: ' . $e->getMessage()], 500);
    }
});
Route::get('/google-signin-test', function() {
    return view('google-signin');
});

// Test route to check if callback route is accessible
Route::get('/test-google-callback', function() {
    return "✅ Google callback route is accessible! Current time: " . now();
});

// Alternative Google callback route (in case /auth/google/callback is blocked)
Route::get('/google-auth-callback', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.callback.alt');

// Simple callback route for testing
Route::get('/oauth/google/return', [SocialAuthController::class, 'handleGoogleCallback'])->name('google.return');

// Test route to check if oauth path works
Route::get('/oauth/test', function() {
    return "OAuth test route works! Time: " . now();
});

// Even simpler test
Route::get('/google-login-success', function() {
    return "Google callback reached successfully! Parameters: " . json_encode(request()->all());
});

// Debug route to check server permissions
Route::get('/debug-server-access', function() {
    $output = "<h2>Server Access Debug</h2>";
    $output .= "Current URL: " . request()->fullUrl() . "<br>";
    $output .= "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
    $output .= "PHP Version: " . phpversion() . "<br>";
    $output .= "Laravel Version: " . app()->version() . "<br>";

    $output .= "<h3>Google OAuth Debug:</h3>";
    $output .= "Expected Redirect URI: " . config('services.google.redirect') . "<br>";
    $output .= "Google Client ID: " . (config('services.google.client_id') ? substr(config('services.google.client_id'), 0, 20) . '...' : 'NOT SET') . "<br>";

    $output .= "<h3>Test Routes:</h3>";
    $output .= "<a href='/test-google-callback'>Test Simple Route</a><br>";
    $output .= "<a href='/google-login-success'>Test Google Success Route</a><br>";
    $output .= "<a href='/oauth/google/return'>Test OAuth Return Route</a><br>";
    $output .= "<a href='/auth/google'>Test Google OAuth (will show redirect URI)</a><br>";

    return $output;
});

// Route to show exact Google OAuth URL being generated
Route::get('/show-google-oauth-url', function() {
    try {
        $googleUrl = Socialite::driver('google')
            ->stateless()
            ->scopes(['openid', 'profile', 'email'])
            ->getTargetUrl();

        return "<h2>Google OAuth URL Debug</h2>" .
               "<p><strong>Generated URL:</strong></p>" .
               "<textarea style='width:100%;height:100px;'>" . $googleUrl . "</textarea>" .
               "<p><strong>Redirect URI in URL:</strong> " .
               (parse_url($googleUrl, PHP_URL_QUERY) ?
                parse_str(parse_url($googleUrl, PHP_URL_QUERY), $params) . ($params['redirect_uri'] ?? 'Not found') :
                'Could not parse') . "</p>" .
               "<p><a href='/debug-server-access'>Back to Debug</a></p>";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});
Route::get('auth/linkedin', [SocialAuthController::class, 'redirectToLinkedIn'])->name('linkedin.redirect');
Route::get('auth/linkedin/callback', [SocialAuthController::class, 'handleLinkedInCallback']);
Shuttle::translatedGroup(function () {

    Route::get('cart', [CartController::class, 'index'])->name('cart.index');
    Route::get('courses', [CourseController::class, 'index'])->name('course.index');
    Route::get('courses/{course_title}', [CourseController::class, 'show'])->name('course.show');
    Route::get('conferences', [ConferenceController::class, 'index'])->name('conference.index');
    Route::get('conferences/{conference_title}', [ConferenceController::class, 'show'])->name('conference.show');
    Route::get('festivals', [FestivalController::class, 'index'])->name('festival.index');
    Route::get('festivals/{festival_title}', [FestivalController::class, 'show'])->name('festival.show');
    Route::get('awards', [AwwardsController::class, 'index'])->name('awward.index');
    Route::get('awards/{awward_title}', [AwwardsController::class, 'show'])->name('awward.show');
    Route::get('blog', [BlogController::class, 'index'])->name('blog.index');
    Route::get('blog/{slug}', [BlogController::class, 'show'])->name('blog.show')->where('slug', '.*');




    Route::middleware('guest')->group(function(){
        Route::view('register', 'auth.register')->name('register');
        Route::post('register', [RegisterController::class, 'store'])->name('register.store');
        Route::view('login', 'auth.login')->name('login');
        Route::post('login', [LoginController::class, 'store'])->name('login.store');

        // Password Reset Routes - Simple version without external dependencies
        Route::get('forgot-password', function() {
            return view('auth.forgot-password');
        })->name('password.request');

        Route::post('forgot-password', function(Request $request) {
            $request->validate(['email' => 'required|email']);

            $user = \App\Models\User::where('email', $request->email)->first();

            if (!$user) {
                return back()->withErrors(['email' => 'We can\'t find a user with that email address.']);
            }

            // Generate simple token
            $token = bin2hex(random_bytes(32));

            // Store in password_resets table
            \DB::table('password_resets')->updateOrInsert(
                ['email' => $user->email],
                [
                    'email' => $user->email,
                    'token' => \Hash::make($token),
                    'created_at' => Carbon::now()
                ]
            );

            // Simple email sending without complex dependencies
            try {
                $resetUrl = url(route('password.reset', ['token' => $token, 'email' => $user->email]));

                \Mail::send('emails.simple-password-reset', [
                    'user' => $user,
                    'resetUrl' => $resetUrl
                ], function($message) use ($user) {
                    $message->to($user->email);
                    $message->subject('Password Reset Request - Business Eagles');
                });

                return back()->with('status', 'We have emailed your password reset link!');
            } catch (\Exception $e) {
                return back()->with('status', 'Password reset link generated. Please check your email.');
            }
        })->name('password.email');

        Route::get('reset-password/{token}', function($token) {
            return view('auth.reset-password', ['token' => $token, 'email' => request('email')]);
        })->name('password.reset');

        Route::post('reset-password', function(Request $request) {
            $request->validate([
                'token' => 'required',
                'email' => 'required|email',
                'password' => 'required|min:8|confirmed',
            ]);

            // Check if token exists and is valid
            $passwordReset = \DB::table('password_resets')
                ->where('email', $request->email)
                ->first();

            if (!$passwordReset || !\Hash::check($request->token, $passwordReset->token)) {
                return back()->withErrors(['email' => 'This password reset token is invalid.']);
            }

            // Check if token is expired (60 minutes)
            $createdAt = Carbon::parse($passwordReset->created_at);
            if ($createdAt->diffInMinutes(Carbon::now()) > 60) {
                return back()->withErrors(['email' => 'This password reset token has expired.']);
            }

            // Find user and update password
            $user = \App\Models\User::where('email', $request->email)->first();
            if (!$user) {
                return back()->withErrors(['email' => 'We can\'t find a user with that email address.']);
            }

            // Update password
            $user->password = \Hash::make($request->password);
            $user->save();

            // Delete the password reset token
            \DB::table('password_resets')->where('email', $request->email)->delete();

            return back()->with('status', 'Your password has been reset successfully! Please login with your new password.');
        })->name('password.update');
    });

    Route::middleware('auth')->group(function(){
        Route::get('profile/{tab?}', [ProfileController::class, 'index'])->name('profile');
        Route::put('profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::get('logout', [ProfileController::class, 'logout'])->name('logout');
        Route::get('package/{package}/checkout', [PackageController::class, 'checkout'])->name('package.checkout');
        Route::post('package/{package}/transaction', [PackageController::class, 'transaction'])->name('package.transaction');
        Route::get('payment/redirect', [TransactionController::class, 'redirect'])->name('payment.redirect');
        Route::get('invoice/{transaction}', [TransactionController::class, 'invoice'])->name('transaction.invoice');
        Route::get('payment/{transaction}/success', [TransactionController::class, 'success'])->name('transaction.success');
    });
});

Shuttle::group(function ()
{
    Route::get('exports/users/download',  [ShuttleExportController::class, 'users'])->name('export.users');
    Route::get('exports/enquiries/download',  [ShuttleExportController::class, 'enquiries'])->name('export.enquiries');

    // Promo Codes routes
    Route::post('promo-codes/generate', [App\Http\Controllers\Shuttle\PromoCodeController::class, 'generate'])->name('promo-codes.generate');
    Route::get('promo-codes/{promoCode}/toggle', [App\Http\Controllers\Shuttle\PromoCodeController::class, 'toggle'])->name('promo-codes.toggle');
    Route::post('conference/{conference}/component',  [ShuttleConferenceController::class, 'addComponent'])->name('conference.component');
    Route::delete('conference/{component}/component',  [ShuttleConferenceController::class, 'destroyComponent'])->name('conference.component.destroy');
    Route::get('conference/{conference}/copy',  [ShuttleConferenceController::class, 'copy'])->name('conference.copy');


	 Route::post('awwards/{awwards}/component',  [ShuttleAwwardsController::class, 'addComponent'])->name('awwards.component');
    Route::delete('awwards/component/{component}',  [ShuttleAwwardsController::class, 'destroyComponent'])->name('awwards.component.destroy');
    Route::get('awwards/{awwards}/copy',  [ShuttleAwwardsController::class, 'copy'])->name('awwards.copy');

     Route::post('festival/{festival}/component',  [ShuttleFestivalController::class, 'addComponent'])->name('festival.component');
    Route::delete('festival/{festival}/component',  [ShuttleFestivalController::class, 'destroyComponent'])->name('festival.component.destroy');
    Route::get('festival/{festival}/copy',  [ShuttleFestivalController::class, 'copy'])->name('festival.copy');

});

// Add this temporary route to fix all blog slugs
Route::get('/fix-all-blog-slugs', function() {
    $blogs = \App\Models\Blog::all();
    $fixed = 0;

    foreach ($blogs as $blog) {
        $originalSlug = $blog->slug;

        // Clean up any existing slug
        if (!empty($blog->slug)) {
            // Remove any ":1" suffix
            $cleanSlug = preg_replace('/:1$/', '', $blog->slug);
            $blog->slug = $cleanSlug;
        } else {
            // Generate slug from title if missing
            $blog->slug = \Illuminate\Support\Str::slug($blog->title);
        }

        if ($originalSlug !== $blog->slug) {
            $blog->save();
            $fixed++;
        }
    }

    return "Fixed {$fixed} blog slugs!";
});

// Add this temporary route to test without middleware
Route::get('/test-blog/{slug}', function($slug) {
    if (is_numeric($slug)) {
        $post = \App\Models\Blog::findOrFail($slug);
    } else {
        $post = \App\Models\Blog::where('slug', $slug)->firstOrFail();
    }

    return [
        'id' => $post->id,
        'title' => $post->title,
        'slug' => $post->slug
    ];
})->withoutMiddleware(['web']); // Specify which middleware to exclude

// Add this route to check URL format
Route::get('/check-url', function() {
    $slug = 'why-attend-sharp-festival-of-marketing-pr-and-communications';
    $blog = \App\Models\Blog::where('slug', $slug)->first();

    if (!$blog) {
        return "Blog not found with slug: {$slug}";
    }

    return [
        'blog_id' => $blog->id,
        'blog_title' => $blog->title,
        'blog_slug' => $blog->slug,
        'url_with_route' => route('blog.show', $blog->slug),
        'direct_url' => url('/blog/' . $blog->slug),
    ];
});

// Add this route at the top level, outside of any groups
Route::get('/direct-blog-test/{slug}', [BlogController::class, 'show'])->name('direct.blog.test');

// Add this route to clear caches
Route::get('/clear-caches', function() {
    \Artisan::call('route:clear');
    \Artisan::call('cache:clear');
    \Artisan::call('config:clear');
    \Artisan::call('view:clear');

    return "All caches cleared!";
});

// Test password reset functionality
Route::get('/test-password-reset', function() {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if (!$user) {
        return "User not found. Please create a user <NAME_EMAIL> first.";
    }

    try {
        // Generate password reset token
        $token = bin2hex(random_bytes(32));

        // Store in password_resets table
        \DB::table('password_resets')->updateOrInsert(
            ['email' => $user->email],
            [
                'email' => $user->email,
                'token' => \Hash::make($token),
                'created_at' => Carbon::now()
            ]
        );

        // Send simple email
        $resetUrl = url(route('password.reset', ['token' => $token, 'email' => $user->email]));

        \Mail::send('emails.simple-password-reset', [
            'user' => $user,
            'resetUrl' => $resetUrl
        ], function($message) use ($user) {
            $message->to($user->email);
            $message->subject('Password Reset Request - Business Eagles');
        });

        return "Password reset email sent successfully to {$user->email}!";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Check database for awward relationship field
Route::get('/check-database', function() {
    try {
        // Check if the relationship field exists
        $result = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->where('field', 'awward_hasmany_awward_package_relationship')
            ->first();

        $output = "<h2>Database Check Results:</h2>";

        if ($result) {
            $output .= "✅ SUCCESS: awward_hasmany_awward_package_relationship field found!<br>";
            $output .= "Field details:<br>";
            $output .= "<pre>" . print_r($result, true) . "</pre>";
        } else {
            $output .= "❌ ERROR: awward_hasmany_awward_package_relationship field NOT found!<br>";
            $output .= "Please run the SQL command again.<br>";
        }

        // Show all fields for awwards (rowable_id = 16)
        $output .= "<hr>";
        $output .= "<h3>All fields for Awards (rowable_id = 16):</h3>";
        $allFields = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->orderBy('ord')
            ->get();

        $output .= "<table border='1' style='border-collapse: collapse;'>";
        $output .= "<tr><th>ID</th><th>Field</th><th>Type</th><th>Display Name</th><th>Edit</th><th>Add</th></tr>";
        foreach ($allFields as $field) {
            $output .= "<tr>";
            $output .= "<td>{$field->id}</td>";
            $output .= "<td>{$field->field}</td>";
            $output .= "<td>{$field->type}</td>";
            $output .= "<td>{$field->display_name}</td>";
            $output .= "<td>" . ($field->edit ? 'Yes' : 'No') . "</td>";
            $output .= "<td>" . ($field->add ? 'Yes' : 'No') . "</td>";
            $output .= "</tr>";
        }
        $output .= "</table>";

        return $output;

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Check if files exist
Route::get('/check-files', function() {
    $output = "<h2>File Check Results:</h2>";

    // Check if edit_add.blade.php exists
    $editAddFile = resource_path('views/shuttle/awwards/edit_add.blade.php');
    if (file_exists($editAddFile)) {
        $output .= "✅ SUCCESS: edit_add.blade.php exists!<br>";
        $output .= "File size: " . filesize($editAddFile) . " bytes<br>";
        $output .= "Last modified: " . date('Y-m-d H:i:s', filemtime($editAddFile)) . "<br>";
    } else {
        $output .= "❌ ERROR: edit_add.blade.php does NOT exist!<br>";
        $output .= "Expected location: {$editAddFile}<br>";
    }

    $output .= "<hr>";

    // Check if directory exists
    $dir = resource_path('views/shuttle/awwards/');
    if (is_dir($dir)) {
        $output .= "✅ SUCCESS: Directory exists!<br>";
        $output .= "Files in directory:<br>";
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $output .= "- {$file}<br>";
            }
        }
    } else {
        $output .= "❌ ERROR: Directory does NOT exist!<br>";
        $output .= "Expected directory: {$dir}<br>";
    }

    $output .= "<hr>";

    // Check if we can read the file content
    if (file_exists($editAddFile)) {
        $output .= "<h3>File Content Preview (first 500 characters):</h3>";
        $output .= "<pre>" . htmlspecialchars(substr(file_get_contents($editAddFile), 0, 500)) . "...</pre>";
    }

    return $output;
});

// Update blogs scaffold interface to use custom controller
Route::get('/update-blogs-controller', function() {
    try {
        $updated = DB::table('shuttle_scaffold_interfaces')
            ->where('id', 5)
            ->update([
                'controller' => 'App\\Http\\Controllers\\Shuttle\\BlogsController'
            ]);

        if ($updated) {
            // Also update browse fields for blogs
            DB::table('shuttle_scaffold_interface_rows')
                ->where('rowable_id', 5)
                ->where('field', 'keyword')
                ->update(['browse' => 1]);

            DB::table('shuttle_scaffold_interface_rows')
                ->where('rowable_id', 5)
                ->where('field', 'description')
                ->update(['browse' => 1]);

            return "✅ SUCCESS: Blogs controller and browse fields updated successfully!<br>
                    Now visit: <a href='/clear-caches'>/clear-caches</a> to clear cache, then check <a href='/mypanel/blogs'>/mypanel/blogs</a>";
        } else {
            return "❌ ERROR: Failed to update blogs controller";
        }

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Update awards video field to support file upload
Route::get('/update-awards-video-field', function() {
    try {
        $updated = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->where('field', 'video')
            ->update([
                'type' => 'image',
                'display_name' => 'Video File'
            ]);

        if ($updated) {
            return "✅ SUCCESS: Awards video field updated to support file upload!<br>
                    Now visit: <a href='/clear-caches'>/clear-caches</a> to clear cache, then check <a href='/mypanel/awards'>/mypanel/awards</a>";
        } else {
            return "❌ ERROR: Failed to update awards video field";
        }

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Debug Google OAuth configuration
Route::get('/debug-google-oauth', function() {
    $output = "<h2>Google OAuth Configuration Debug</h2>";

    $output .= "<h3>Environment Variables:</h3>";
    $output .= "GOOGLE_CLIENT_ID: " . (env('GOOGLE_CLIENT_ID') ? 'SET (' . substr(env('GOOGLE_CLIENT_ID'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "GOOGLE_CLIENT_SECRET: " . (env('GOOGLE_CLIENT_SECRET') ? 'SET (' . substr(env('GOOGLE_CLIENT_SECRET'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "GOOGLE_REDIRECT_URI: " . (env('GOOGLE_REDIRECT_URI') ?: 'NOT SET') . "<br>";

    $output .= "<h3>Config Values:</h3>";
    $output .= "services.google.client_id: " . (config('services.google.client_id') ? 'SET (' . substr(config('services.google.client_id'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "services.google.client_secret: " . (config('services.google.client_secret') ? 'SET (' . substr(config('services.google.client_secret'), 0, 10) . '...)' : 'NOT SET') . "<br>";
    $output .= "services.google.redirect: " . (config('services.google.redirect') ?: 'NOT SET') . "<br>";

    $output .= "<h3>Current URL:</h3>";
    $output .= "Current domain: " . request()->getHost() . "<br>";
    $output .= "Expected callback: " . url('/auth/google/callback') . "<br>";

    $output .= "<h3>Test Links:</h3>";
    $output .= "<a href='/auth/google'>Test Google Login</a><br>";
    $output .= "<a href='/clear-caches'>Clear Caches</a><br>";

    return $output;
});

// Add the missing relationship field to database
Route::get('/add-awward-relationship', function() {
    try {
        // Check if field already exists
        $exists = DB::table('shuttle_scaffold_interface_rows')
            ->where('rowable_id', 16)
            ->where('field', 'awward_hasmany_awward_package_relationship')
            ->exists();

        if ($exists) {
            return "✅ Field already exists!";
        }

        // Insert the new relationship field
        $inserted = DB::table('shuttle_scaffold_interface_rows')->insert([
            'rowable_type' => 'Sina\\Shuttle\\Models\\ScaffoldInterface',
            'rowable_id' => 16,
            'parent_id' => 0,
            'field' => 'awward_hasmany_awward_package_relationship',
            'type' => 'relationship',
            'display_name' => 'awward_packages',
            'required' => 0,
            'browse' => 0,
            'read' => 1,
            'edit' => 1,
            'add' => 1,
            'delete' => 0,
            'details' => json_encode([
                'key' => 'id',
                'type' => 'hasMany',
                'label' => 'title',
                'model' => 'App\\Models\\AwwardPackage',
                'pivot' => null,
                'table' => 'awwards_packages',
                'column' => 'awward_id',
                'taggable' => null,
                'pivot_table' => null
            ]),
            'ord' => 18,
            'last_upd' => 0
        ]);

        if ($inserted) {
            return "✅ SUCCESS: awward_hasmany_awward_package_relationship field added successfully!<br>
                    Now visit: <a href='/clear-caches'>/clear-caches</a> to clear cache, then check <a href='/mypanel/awards/create'>/mypanel/awards/create</a>";
        } else {
            return "❌ ERROR: Failed to insert field";
        }

    } catch (Exception $e) {
        return "❌ ERROR: " . $e->getMessage();
    }
});

// Add this route at the top level, outside of any groups
Route::get('/dirblog/{slug}', function($slug) {
    // Remove any ":1" suffix that might be in the slug
    $cleanSlug = preg_replace('/:1$/', '', $slug);

    // Check if the parameter is numeric (ID) or a string (slug)
    if (is_numeric($cleanSlug)) {
        $post = \App\Models\Blog::findOrFail($cleanSlug);
    } else {
        $post = \App\Models\Blog::where('slug', $cleanSlug)->firstOrFail();
    }

    return view('blog.show', compact('post'));
})->name('dirblog.show');

// Add a direct route for the blog index page
Route::get('/blog', [App\Http\Controllers\BlogController::class, 'index'])->name('blog.index');

// Test Google OAuth
Route::get('/test-google-login', function() {
    try {
        $output = "<h2>Google OAuth Test</h2>";

        $output .= "<h3>Configuration:</h3>";
        $output .= "Client ID: " . (config('services.google.client_id') ? 'SET' : 'NOT SET') . "<br>";
        $output .= "Client Secret: " . (config('services.google.client_secret') ? 'SET' : 'NOT SET') . "<br>";
        $output .= "Redirect URI: " . config('services.google.redirect') . "<br>";

        $output .= "<h3>Test Links:</h3>";
        $output .= "<a href='/auth/google' target='_blank'>Try Google Login</a><br>";

        return $output;
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Test email sending
Route::get('/test-invoice-email', function() {
    try {
        // Create a test PDF path (you can use an existing invoice)
        $testPdfPath = storage_path('app/invoices/test-invoice.pdf');

        // Create a simple test PDF if it doesn't exist
        if (!file_exists($testPdfPath)) {
            $invoiceDirectory = storage_path('app/invoices');
            if (!file_exists($invoiceDirectory)) {
                mkdir($invoiceDirectory, 0755, true);
            }

            $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML('<h1>Test Invoice</h1><p>This is a test invoice.</p>');
            $pdf->save($testPdfPath);
        }

        // Send test email
        \Illuminate\Support\Facades\Mail::to('<EMAIL>')->send(new \App\Mail\InvoiceEmail(
            $testPdfPath,
            'TEST-001',
            now()->addDays(7)->format('Y-m-d'),
            'Test Customer',
            'Test Package Title'
        ));

        return "✅ Test invoice email sent <NAME_EMAIL>!";

    } catch (\Exception $e) {
        return "❌ Email test failed: " . $e->getMessage() . "<br>Trace: " . $e->getTraceAsString();
    }
});

// Search route
Route::get('/search', [SearchController::class, 'index'])->name('search');

// Blog search route
Route::get('/blog-search', [SearchController::class, 'blogSearch'])->name('blog.search');

// Debug Awards scaffold interface
Route::get('/debug-awards-scaffold', function() {
    $scaffold = DB::table('shuttle_scaffold_interfaces')
        ->where('model', 'App\\Models\\Awward')
        ->first();

    if ($scaffold) {
        return "Awards Scaffold Interface:<br>" .
               "ID: " . $scaffold->id . "<br>" .
               "Name: " . $scaffold->name . "<br>" .
               "Slug: " . $scaffold->slug . "<br>" .
               "Model: " . $scaffold->model . "<br>" .
               "Controller: " . $scaffold->controller . "<br><br>" .
               "Delete Route should be: " . route('shuttle.scaffold_interface.destroy', ['scaffold_interface' => $scaffold->slug, 'id' => '__id']);
    } else {
        return "Awards scaffold interface not found!";
    }
});

// Hero Slider Component Fields Fix
Route::get('/fix-hero-slider', function () {
    $component = \Sina\Shuttle\Models\Component::where('name', 'hero_slider')->first();

    if (!$component) {
        return 'Hero Slider component not found';
    }

    // Delete existing rows
    $component->allRows()->delete();

    // Create main array field
    $arrayRow = $component->allRows()->create([
        'field' => 'a',
        'type' => 'array',
        'display_name' => '',
        'parent_id' => 0,
        'required' => 0,
        'browse' => 0,
        'read' => 0,
        'edit' => 0,
        'add' => 0,
        'delete' => 0,
        'details' => [],
        'ord' => 0,
        'last_upd' => time()
    ]);

    // Create child fields
    $fields = [
        ['field' => 'image', 'type' => 'image', 'display_name' => 'img'],
        ['field' => 'title', 'type' => 'text', 'display_name' => 'title'],
        ['field' => 'description', 'type' => 'text', 'display_name' => 'desc'],
        ['field' => 'link', 'type' => 'text', 'display_name' => 'link'],
        ['field' => 'btn-text', 'type' => 'text', 'display_name' => 'btn text']
    ];

    foreach ($fields as $field) {
        $component->allRows()->create([
            'field' => $field['field'],
            'type' => $field['type'],
            'display_name' => $field['display_name'],
            'parent_id' => $arrayRow->id,
            'required' => 0,
            'browse' => 0,
            'read' => 0,
            'edit' => 0,
            'add' => 0,
            'delete' => 0,
            'details' => null,
            'ord' => 0,
            'last_upd' => time()
        ]);
    }

    return 'Hero Slider component fields created successfully! Now visit <a href="https://business-eagles.com/mypanel/page/component/442">Hero Slider Component</a> to test.';
});

// Find contact_form component details
Route::get('/contact-form-details', function () {
    $component = \Sina\Shuttle\Models\Component::where('name', 'contact_form')->first();

    if (!$component) {
        return 'Contact Form component not found';
    }

    $html = "<h2>Contact Form Component Details:</h2>";
    $html .= "<p><strong>ID:</strong> {$component->id}</p>";
    $html .= "<p><strong>Name:</strong> {$component->name}</p>";
    $html .= "<p><strong>Display Name:</strong> {$component->display_name}</p>";
    $html .= "<p><a href='https://business-eagles.com/mypanel/page/component/{$component->id}' target='_blank'>Edit Component</a></p>";

    $html .= "<h3>Current Fields:</h3>";
    $html .= "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    $html .= "<tr><th>Field</th><th>Type</th><th>Display Name</th><th>Required</th><th>Edit</th></tr>";

    foreach ($component->allRows as $row) {
        $html .= "<tr>";
        $html .= "<td>{$row->field}</td>";
        $html .= "<td>{$row->type}</td>";
        $html .= "<td>{$row->display_name}</td>";
        $html .= "<td>" . ($row->required ? 'Yes' : 'No') . "</td>";
        $html .= "<td>" . ($row->edit ? 'Yes' : 'No') . "</td>";
        $html .= "</tr>";
    }
    $html .= "</table>";

    $html .= "<h3>Component Template:</h3>";
    $templatePath = resource_path('views/components/contact_form.blade.php');
    if (file_exists($templatePath)) {
        $html .= "<p><strong>Template exists:</strong> {$templatePath}</p>";
    } else {
        $html .= "<p><strong>Template missing:</strong> {$templatePath}</p>";
    }

    return $html;
});

// Add map functionality to contact_form
Route::get('/add-map-to-contact-form', function () {
    $component = \Sina\Shuttle\Models\Component::where('name', 'contact_form')->first();

    if (!$component) {
        return 'Contact Form component not found';
    }

    // Add map-lat field if not exists
    $latField = \Sina\Shuttle\Models\ScaffoldinterfaceRow::where([
        'rowable_type' => 'Sina\Shuttle\Models\Component',
        'rowable_id' => $component->id,
        'field' => 'map-lat'
    ])->first();

    if (!$latField) {
        \Sina\Shuttle\Models\ScaffoldinterfaceRow::create([
            'rowable_type' => 'Sina\Shuttle\Models\Component',
            'rowable_id' => $component->id,
            'field' => 'map-lat',
            'type' => 'hidden',
            'display_name' => 'Map Latitude',
            'required' => false,
            'browse' => false,
            'read' => true,
            'edit' => true,
            'add' => true,
            'delete' => false,
            'ord' => 100,
            'parent_id' => 0,
            'last_upd' => time()
        ]);
    }

    // Add map-lng field if not exists
    $lngField = \Sina\Shuttle\Models\ScaffoldinterfaceRow::where([
        'rowable_type' => 'Sina\Shuttle\Models\Component',
        'rowable_id' => $component->id,
        'field' => 'map-lng'
    ])->first();

    if (!$lngField) {
        \Sina\Shuttle\Models\ScaffoldinterfaceRow::create([
            'rowable_type' => 'Sina\Shuttle\Models\Component',
            'rowable_id' => $component->id,
            'field' => 'map-lng',
            'type' => 'hidden',
            'display_name' => 'Map Longitude',
            'required' => false,
            'browse' => false,
            'read' => true,
            'edit' => true,
            'add' => true,
            'delete' => false,
            'ord' => 101,
            'parent_id' => 0,
            'last_upd' => time()
        ]);
    }

    return 'Map fields added to contact_form component! <a href="https://business-eagles.com/contact-form-details">Check Details</a> | <a href="https://business-eagles.com/mypanel/page/component/' . $component->id . '">Edit Component</a>';
});

// Find which page has contact_form component
Route::get('/find-contact-form-page', function () {
    $component = \Sina\Shuttle\Models\Component::where('name', 'contact_form')->first();

    if (!$component) {
        return 'Contact Form component not found';
    }

    // Find pages that use this component
    $pages = \Sina\Shuttle\Models\Page::whereHas('components', function($query) use ($component) {
        $query->where('component_id', $component->id);
    })->get();

    $html = "<h2>Pages using contact_form component:</h2>";

    if ($pages->count() > 0) {
        foreach ($pages as $page) {
            $html .= "<p><strong>Page:</strong> {$page->title} - <strong>URL:</strong> <a href='https://business-eagles.com/{$page->url}' target='_blank'>/{$page->url}</a></p>";
        }
    } else {
        $html .= "<p>No pages found using contact_form component</p>";

        // Let's check all page components
        $allPageComponents = \Sina\Shuttle\Models\PageComponent::all();
        $html .= "<h3>All Page Components:</h3>";
        foreach ($allPageComponents as $pc) {
            $page = $pc->page;
            $comp = $pc->component;
            if ($comp && $comp->name === 'contact_form') {
                $html .= "<p>Found contact_form on page: {$page->title} ({$page->url})</p>";
            }
        }
    }

    return $html;
});

// Find main_about component
Route::get('/find-main-about', function () {
    $components = \Sina\Shuttle\Models\Component::where('name', 'like', '%main%about%')
        ->orWhere('name', 'like', '%about%main%')
        ->orWhere('display_name', 'like', '%main%about%')
        ->orWhere('display_name', 'like', '%about%main%')
        ->get();

    $html = "<h2>Main About Component Search Results:</h2>";

    if ($components->count() > 0) {
        foreach ($components as $component) {
            $html .= "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px;'>";
            $html .= "<h3>Component: {$component->name}</h3>";
            $html .= "<p>Display Name: {$component->display_name}</p>";
            $html .= "<p>ID: {$component->id}</p>";
            $html .= "<p>Template File: resources/views/components/{$component->name}.blade.php</p>";
            $html .= "<a href='https://business-eagles.com/mypanel/page/component/{$component->id}' target='_blank'>Edit Component</a>";
            $html .= "</div>";
        }
    } else {
        $html .= "<p>No main_about component found. Let's check all components with 'about' in name:</p>";

        $aboutComponents = \Sina\Shuttle\Models\Component::where('name', 'like', '%about%')
            ->orWhere('display_name', 'like', '%about%')
            ->get();

        foreach ($aboutComponents as $component) {
            $html .= "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px;'>";
            $html .= "<h3>Component: {$component->name}</h3>";
            $html .= "<p>Display Name: {$component->display_name}</p>";
            $html .= "<p>ID: {$component->id}</p>";
            $html .= "<p>Template File: resources/views/components/{$component->name}.blade.php</p>";
            $html .= "<a href='https://business-eagles.com/mypanel/page/component/{$component->id}' target='_blank'>Edit Component</a>";
            $html .= "</div>";
        }
    }

    return $html;
});

// Check Homepage components in detail
Route::get('/check-homepage-components', function () {
    $page = \Sina\Shuttle\Models\Page::where('url', 'home')->with(['components'])->first();

    if (!$page) {
        return "Homepage not found!";
    }

    $html = "<h2>Homepage Components:</h2>";

    foreach ($page->components as $index => $component) {
        $html .= "<div style='border: 2px solid #007cba; padding: 15px; margin: 10px; background: #f9f9f9;'>";
        $html .= "<h3>Component #" . ($index + 1) . ": {$component->name}</h3>";
        $html .= "<p><strong>Display Name:</strong> {$component->display_name}</p>";
        $html .= "<p><strong>Component ID:</strong> {$component->id}</p>";
        $html .= "<p><strong>Template File:</strong> resources/views/components/{$component->name}.blade.php</p>";

        // Check if template file exists
        $templatePath = resource_path("views/components/{$component->name}.blade.php");
        $html .= "<p><strong>Template Exists:</strong> " . (file_exists($templatePath) ? '✅ YES' : '❌ NO') . "</p>";

        // Show component settings
        if ($component->pivot && $component->pivot->setting) {
            $html .= "<p><strong>Component Data:</strong></p>";
            $html .= "<pre style='background: #eee; padding: 10px; max-height: 200px; overflow-y: auto;'>";
            $html .= htmlspecialchars(json_encode($component->pivot->setting, JSON_PRETTY_PRINT));
            $html .= "</pre>";
        }

        $html .= "<a href='https://business-eagles.com/mypanel/page/component/{$component->id}' target='_blank' style='background: #007cba; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>Edit Component</a>";
        $html .= "</div>";
    }

    return $html;
});

// Debug about_us_full component data
Route::get('/debug-about-us-full', function () {
    $component = \Sina\Shuttle\Models\Component::where('name', 'about_us_full')->first();

    if (!$component) {
        return 'about_us_full component not found';
    }

    // Get homepage
    $page = \Sina\Shuttle\Models\Page::where('url', 'home')->with(['components'])->first();

    $html = "<h2>About Us Full Component Debug:</h2>";
    $html .= "<p><strong>Component ID:</strong> {$component->id}</p>";
    $html .= "<p><strong>Component Name:</strong> {$component->name}</p>";

    // Find this component on homepage
    $pageComponent = null;
    foreach ($page->components as $comp) {
        if ($comp->id == $component->id) {
            $pageComponent = $comp;
            break;
        }
    }

    if ($pageComponent && $pageComponent->pivot && $pageComponent->pivot->setting) {
        $html .= "<h3>Component Data on Homepage:</h3>";
        $settings = $pageComponent->pivot->setting;

        $html .= "<p><strong>Text field content:</strong></p>";
        $html .= "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
        $html .= "<strong>Raw data:</strong><br>";
        $html .= "<pre>" . htmlspecialchars(data_get($settings, 'text', 'NOT FOUND')) . "</pre>";
        $html .= "</div>";

        $html .= "<p><strong>All component data:</strong></p>";
        $html .= "<pre style='background: #eee; padding: 10px; max-height: 300px; overflow-y: auto;'>";
        $html .= htmlspecialchars(json_encode($settings, JSON_PRETTY_PRINT));
        $html .= "</pre>";
    } else {
        $html .= "<p>❌ Component not found on homepage or no data!</p>";
    }

    // Show template content
    $templatePath = resource_path("views/components/about_us_full.blade.php");
    if (file_exists($templatePath)) {
        $html .= "<h3>Template Content:</h3>";
        $html .= "<pre style='background: #eee; padding: 10px;'>";
        $html .= htmlspecialchars(file_get_contents($templatePath));
        $html .= "</pre>";
    }

    return $html;
});


// Redirect all unknown routes (404) to the homepage
Route::fallback(function () {
    return redirect('/');
});
